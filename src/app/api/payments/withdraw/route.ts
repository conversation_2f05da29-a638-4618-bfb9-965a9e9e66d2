
import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWallet, upsertWallet } from '@/app/api/payments/repos/wallets-repo';
import { addWithdrawal, getWithdrawalById } from '@/app/api/payments/repos/withdrawals-repo';
import { appendTransaction, findByWithdrawalId } from '@/app/api/payments/repos/transactions-repo';
import { PaymentsService } from '@/app/api/payments/services/payments-service';
import { ok, err, RefreshHints, ErrorCodes } from '@/lib/http/envelope';
import { logWithdrawalTransition, logWalletChange } from '@/lib/log/transitions';
import type { UserType } from '@/app/api/payments/repos/wallets-repo';

export async function POST(req: NextRequest) {
  try {
    // 🔒 Auth
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        err({
          code: ErrorCodes.UNAUTHORIZED,
          message: 'Authentication required',
          status: 401,
        }),
        { status: 401 }
      );
    }

    const body = await req.json();
    const { amount, currency, withdrawalId, userType } = body;
    const userId = parseInt(session.user.id);
    const finalUserType: UserType = userType || 'freelancer'; // Default to freelancer
    const finalCurrency = currency || 'USD';

    // Validate input
    if (typeof amount !== "number" || isNaN(amount) || amount <= 0) {
      return NextResponse.json(
        err({
          code: ErrorCodes.INVALID_INPUT,
          message: "Amount must be a positive number",
          status: 400,
        }),
        { status: 400 }
      );
    }

    // 🔒 Idempotency check: if withdrawalId provided, check for existing withdrawal
    if (withdrawalId) {
      const existingWithdrawal = await getWithdrawalById(withdrawalId);
      if (existingWithdrawal) {
        // Get current wallet state for response
        const wallet = await getWallet(userId, finalUserType, finalCurrency);

        return NextResponse.json(
          ok({
            entities: {
              withdrawal: existingWithdrawal,
              wallet: wallet ? {
                availableBalance: wallet.availableBalance,
                pendingWithdrawals: wallet.pendingWithdrawals,
                totalWithdrawn: wallet.totalWithdrawn,
                currency: wallet.currency,
              } : null,
            },
            refreshHints: [RefreshHints.WALLET_SUMMARY, RefreshHints.TRANSACTIONS_LIST],
            notificationsQueued: false,
            message: 'Idempotent success (withdrawal already exists)',
          })
        );
      }
    }

    // Load wallet using repo
    let wallet = await getWallet(userId, finalUserType, finalCurrency);
    if (!wallet) {
      // Create new wallet if it doesn't exist
      wallet = {
        userId,
        userType: finalUserType,
        currency: finalCurrency,
        availableBalance: 0,
        pendingWithdrawals: 0,
        lifetimeEarnings: 0,
        totalWithdrawn: 0,
        holds: 0,
        updatedAt: new Date().toISOString(),
      };
      await upsertWallet(wallet);
    }

    // Use service for business logic
    const holdResult = PaymentsService.holdWithdrawal(wallet, amount);
    if (!holdResult.ok) {
      return NextResponse.json(
        err({
          code: ErrorCodes.INSUFFICIENT_FUNDS,
          message: holdResult.reason,
          status: 400,
        }),
        { status: 400 }
      );
    }

    // Extract the updated wallet from the successful result
    const updatedWallet = holdResult.data!;

    // Generate withdrawal ID if not provided
    const finalWithdrawalId = withdrawalId || `WD-${userId}-${Date.now()}`;
    const now = new Date().toISOString();

    // Create withdrawal record
    const withdrawal = {
      withdrawalId: finalWithdrawalId,
      userId,
      userType: finalUserType,
      amount,
      currency: finalCurrency,
      status: 'pending' as const,
      requestedAt: now,
    };

    // Save withdrawal
    await addWithdrawal(withdrawal);

    // Update wallet with held funds
    await upsertWallet(updatedWallet);

    // Create transaction record
    const transaction = {
      transactionId: `TXN-${finalWithdrawalId}`,
      type: 'withdrawal' as const,
      integration: 'mock' as const,
      status: 'processing' as const,
      amount,
      currency: finalCurrency,
      timestamp: now,
      withdrawalId: finalWithdrawalId,
      freelancerId: finalUserType === 'freelancer' ? userId : undefined,
      commissionerId: finalUserType === 'commissioner' ? userId : undefined,
      metadata: {
        withdrawalId: finalWithdrawalId,
        userType: finalUserType,
      },
    };

    await appendTransaction(transaction);

    // Log transitions for observability
    logWithdrawalTransition(
      finalWithdrawalId,
      'requested',
      'pending',
      userId,
      {
        amount,
        currency: finalCurrency,
        method: 'pending_review',
      }
    );

    logWalletChange(
      userId,
      finalUserType,
      'hold',
      amount,
      finalCurrency,
      userId,
      {
        reason: 'withdrawal_request',
        withdrawalId: finalWithdrawalId,
        previousBalance: wallet.availableBalance,
        newBalance: updatedWallet.availableBalance,
      }
    );

    return NextResponse.json(
      ok({
        entities: {
          withdrawal: {
            withdrawalId: finalWithdrawalId,
            amount,
            currency: finalCurrency,
            status: 'pending',
            requestedAt: now,
          },
          wallet: {
            availableBalance: updatedWallet.availableBalance,
            pendingWithdrawals: updatedWallet.pendingWithdrawals,
            totalWithdrawn: updatedWallet.totalWithdrawn,
            currency: updatedWallet.currency,
          },
          transaction: {
            transactionId: transaction.transactionId,
            status: transaction.status,
            amount: transaction.amount,
            currency: transaction.currency,
          },
        },
        refreshHints: [
          RefreshHints.WALLET_SUMMARY,
          RefreshHints.TRANSACTIONS_LIST,
          RefreshHints.DASHBOARD,
        ],
        notificationsQueued: false, // Withdrawals don't generate notifications
        message: 'Withdrawal request created successfully',
      })
    );
  } catch (error) {
    console.error('[WITHDRAWAL_REQUEST_ERROR]', error);
    return NextResponse.json(
      err({
        code: ErrorCodes.INTERNAL_ERROR,
        message: 'Internal server error',
        status: 500,
      }),
      { status: 500 }
    );
  }
}