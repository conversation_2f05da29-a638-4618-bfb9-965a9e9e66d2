import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getInvoiceByNumber, updateInvoice } from '@/app/api/payments/repos/invoices-repo';
import { appendTransaction, listByInvoiceNumber } from '@/app/api/payments/repos/transactions-repo';
import { getProjectById } from '@/app/api/payments/repos/projects-repo';
import { processMockPayment } from '../utils/gateways/test-gateway';
import { ok, err, RefreshHints, ErrorCodes } from '@/lib/http/envelope';
import { logInvoiceTransition } from '@/lib/log/transitions';
import type { InvoiceLike, ProjectLike, TaskLike, InvoicingMethod, ProjectStatus } from '@/app/api/payments/domain/types';

// Optional env flags for future real gateways
const useStripe = process.env.PAYMENT_GATEWAY_STRIPE === 'true';
const usePaystack = process.env.PAYMENT_GATEWAY_PAYSTACK === 'true';
const usePayPal = process.env.PAYMENT_GATEWAY_PAYPAL === 'true';

// Feature flag for eligibility checks (can be disabled for tests)
const requireEligibility = process.env.PAYMENTS_REQUIRE_ELIGIBILITY !== 'false';

export async function POST(req: Request) {
  try {
    // 🔒 Auth
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        err({
          code: ErrorCodes.UNAUTHORIZED,
          message: 'Authentication required',
          status: 401,
        }),
        { status: 401 }
      );
    }

    const { invoiceNumber, freelancerId } = await req.json();
    const sessionUserId = parseInt(session.user.id);

    // Validate body
    if (!invoiceNumber || !freelancerId) {
      return NextResponse.json(
        err({
          code: ErrorCodes.MISSING_REQUIRED_FIELD,
          message: 'Missing invoiceNumber or freelancerId',
          status: 400,
        }),
        { status: 400 }
      );
    }

    // 🔒 Only the owner freelancer may trigger
    if (Number(freelancerId) !== sessionUserId) {
      return NextResponse.json(
        err({
          code: ErrorCodes.FORBIDDEN,
          message: 'You can only trigger payments for your own invoices',
          status: 403,
        }),
        { status: 403 }
      );
    }

    // Load invoice
    const invoice = await getInvoiceByNumber(String(invoiceNumber));
    if (!invoice) {
      return NextResponse.json(
        err({
          code: ErrorCodes.RESOURCE_NOT_FOUND,
          message: 'Invoice not found',
          status: 404,
        }),
        { status: 404 }
      );
    }

    // 🔒 Ensure invoice belongs to freelancer
    if (Number(invoice.freelancerId) !== Number(freelancerId)) {
      return NextResponse.json(
        err({
          code: ErrorCodes.FORBIDDEN,
          message: 'Invoice does not belong to you',
          status: 403,
        }),
        { status: 403 }
      );
    }

    // Status guards
    if (invoice.status === 'paid') {
      return NextResponse.json(
        err({
          code: ErrorCodes.DUPLICATE_REQUEST,
          message: 'Invoice already paid',
          status: 409,
        }),
        { status: 409 }
      );
    }
    if (invoice.status !== 'sent') {
      return NextResponse.json(
        err({
          code: ErrorCodes.INVALID_STATUS,
          message: 'Invoice must be in "sent" status to trigger payment',
          status: 400,
        }),
        { status: 400 }
      );
    }

    // Project lookup
    if (!invoice.projectId) {
      return NextResponse.json(
        err({
          code: ErrorCodes.INVALID_INPUT,
          message: 'Invoice has no associated project',
          status: 400,
        }),
        { status: 400 }
      );
    }
    const projectRaw = await getProjectById(Number(invoice.projectId));
    if (!projectRaw) {
      return NextResponse.json(
        err({
          code: ErrorCodes.RESOURCE_NOT_FOUND,
          message: 'Project not found',
          status: 404,
        }),
        { status: 404 }
      );
    }

    // Normalize to domain unions (hard-narrow strings)
    const normalizedMethod = (projectRaw?.invoicingMethod === 'completion' ? 'completion' : 'milestone') as InvoicingMethod;

    const rawStatusVal = String(projectRaw?.status ?? 'ongoing');
    const allowedStatuses: ProjectStatus[] = ['proposed','ongoing','paused','completed','archived'];
    const normalizedStatus = (allowedStatuses as readonly string[]).includes(rawStatusVal)
      ? (rawStatusVal as ProjectStatus)
      : ('ongoing' as ProjectStatus);

    // 🔎 Call payment-eligibility endpoint first to ensure UI can safely show "Pay now"
    // Can be disabled via PAYMENTS_REQUIRE_ELIGIBILITY=false for tests
    if (requireEligibility) {
      try {
        const proto = req.headers.get('x-forwarded-proto') ?? 'http';
        const host = req.headers.get('host') ?? 'localhost:3000';

        // Validate headers are present (important for production)
        if (!proto || !host) {
          console.warn('[payments.trigger] Missing forwarded headers, using defaults');
        }

        const base = `${proto}://${host}`;
        const eligRes = await fetch(`${base}/api/projects/payment-eligibility?projectId=${invoice.projectId}`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          // Add timeout to prevent hanging
          signal: AbortSignal.timeout(5000)
        });

        if (!eligRes.ok) {
          return NextResponse.json(
            err({
              code: ErrorCodes.SERVICE_UNAVAILABLE,
              message: `Failed to verify payment eligibility (${eligRes.status})`,
              status: 502,
            }),
            { status: 502 }
          );
        }

        const eligibility = await eligRes.json();
        if (!eligibility?.paymentEligible) {
          return NextResponse.json(
            err({
              code: ErrorCodes.INVALID_STATUS,
              message: 'Project not eligible for payment yet',
              status: 403,
            }),
            { status: 403 }
          );
        }
      } catch (e) {
        console.warn('[payments.trigger] eligibility check failed', e);
        return NextResponse.json(
          err({
            code: ErrorCodes.SERVICE_UNAVAILABLE,
            message: 'Eligibility check failed',
            status: 502,
          }),
          { status: 502 }
        );
      }
    } else {
      console.log('[payments.trigger] Eligibility check skipped (PAYMENTS_REQUIRE_ELIGIBILITY=false)');
    }

    // Prevent duplicate trigger by checking tx log first
    const existing = await listByInvoiceNumber(String(invoiceNumber));
    const hasOpenTx = existing.some(tx => tx.status === 'processing' || tx.status === 'paid');
    if (hasOpenTx) {
      return NextResponse.json({ error: 'Payment already triggered for this invoice', existingTransactionId: existing[0]?.transactionId }, { status: 409 });
    }

    // Gateway placeholders (real integrations later)
    if (useStripe) {
      console.log('[payments.trigger] Stripe placeholder');
    } else if (usePaystack) {
      console.log('[payments.trigger] Paystack placeholder');
    } else if (usePayPal) {
      console.log('[payments.trigger] PayPal placeholder');
    } else {
      console.log('[payments.trigger] Using mock gateway');
    }

    // Build transaction via mock gateway (processing)
    const paymentRecord = await processMockPayment({
      invoiceNumber: invoice.invoiceNumber,
      projectId: Number(invoice.projectId),
      freelancerId: Number(invoice.freelancerId),
      commissionerId: Number(invoice.commissionerId),
      totalAmount: Number(invoice.totalAmount)
    }, 'trigger');

    // Persist: set invoice → processing
    const updateOk = await updateInvoice(String(invoiceNumber), {
      status: 'processing',
      updatedAt: new Date().toISOString()
    });
    if (!updateOk) {
      return NextResponse.json(
        err({
          code: ErrorCodes.INTERNAL_ERROR,
          message: 'Failed to update invoice status',
          status: 500,
        }),
        { status: 500 }
      );
    }

    // Log the status transition
    logInvoiceTransition(
      String(invoiceNumber),
      'sent',
      'processing',
      sessionUserId,
      {
        projectId: Number(invoice.projectId),
        amount: Number(invoice.totalAmount),
        integration: 'mock',
        transactionId: paymentRecord.transactionId,
      }
    );

    // Persist: append transaction record
    await appendTransaction({
      ...paymentRecord,
      type: 'invoice',
      integration: 'mock'
    } as any);

    return NextResponse.json(
      ok({
        entities: {
          invoice: {
            invoiceNumber: invoice.invoiceNumber,
            status: 'processing',
            amount: invoice.totalAmount,
            projectId: invoice.projectId,
          },
          transaction: {
            transactionId: paymentRecord.transactionId,
            integration: paymentRecord.integration,
            status: 'processing',
          },
        },
        refreshHints: [
          RefreshHints.INVOICES_LIST,
          RefreshHints.INVOICE_DETAIL,
          RefreshHints.TRANSACTIONS_LIST,
          RefreshHints.DASHBOARD,
        ],
        notificationsQueued: false,
        message: 'Payment request initiated successfully',
      })
    );
  } catch (error) {
    console.error('[PAYMENT_TRIGGER_ERROR]', error);
    return NextResponse.json(
      err({
        code: ErrorCodes.INTERNAL_ERROR,
        message: 'Internal server error',
        status: 500,
      }),
      { status: 500 }
    );
  }
}